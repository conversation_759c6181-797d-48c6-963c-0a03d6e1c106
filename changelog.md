# Change Log

All notable changes to the "aicode" extension will be documented in this file.

Check [Keep a Changelog](http://keepachangelog.com/) for recommendations on how to structure this file.

## [0.0.4] - 2025-01-30
### 简化
- 移除直接启动方式，只保留CLI启动方式
- 简化服务控制命令，统一使用 `mastra dev` 启动
- 更新文档和测试，专注于CLI方式

### 修复
- 修复启动方式选择逻辑
- 优化服务状态检查
- 清理不必要的代码和文件

## [0.0.3] - 2025-01-30
### 新增
- 添加Mastra服务自动启动功能
- 支持两种启动方式：直接启动和CLI启动
- 新增服务控制命令
- 添加配置选项

## [0.0.2] - 2025-01-30
- 基础Mastra集成

## [0.0.1] - 2024-01-01
- 初始版本发布

## [Unreleased]

- Initial release