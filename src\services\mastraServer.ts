import * as vscode from 'vscode';
import * as path from 'path';
import { spawn, ChildProcess } from 'child_process';

let mastraProcess: ChildProcess | null = null;
let serverPort = 4111;

export function startMastraServer(context: vscode.ExtensionContext): void {
    // 检查是否已经有服务在运行
    if (mastraProcess) {
        console.log('Mastra server is already running');
        return;
    }

    try {
        // 获取扩展根目录
        const extensionPath = context.extensionUri.fsPath;
        
        console.log('Starting Mastra server...');
        console.log('Extension path:', extensionPath);

        // 方案1：直接运行mastra dev命令
        mastraProcess = spawn('npx', ['mastra', 'dev'], {
            cwd: extensionPath,
            stdio: ['ignore', 'pipe', 'pipe'],
            shell: true
        });
        console.log('执行对应的命令',mastraProcess);

        if (mastraProcess.stdout) {
            mastraProcess.stdout.on('data', (data) => {
                const output = data.toString();
                console.log('Mastra server output:', output);
                
                // 检查服务是否成功启动
                if (output.includes('Server running on') || output.includes('localhost:4111')) {
                    vscode.window.showInformationMessage('Mastra AI服务已启动在端口4111');
                }
            });
        }

        if (mastraProcess.stderr) {
            mastraProcess.stderr.on('data', (data) => {
                const error = data.toString();
                console.error('Mastra server error:', error);
                
                // 如果是端口占用错误，尝试其他端口
                if (error.includes('EADDRINUSE') || error.includes('port') && error.includes('already')) {
                    console.log('Port 4111 is busy, trying alternative approach...');
                    tryAlternativePort(context);
                }
            });
        }

        mastraProcess.on('close', (code) => {
            console.log(`Mastra server process exited with code ${code}`);
            mastraProcess = null;
            
            if (code !== 0) {
                vscode.window.showWarningMessage(`Mastra服务异常退出 (code: ${code})`);
            }
        });

        mastraProcess.on('error', (error) => {
            console.error('Failed to start Mastra server:', error);
            vscode.window.showErrorMessage(`启动Mastra服务失败: ${error.message}`);
            mastraProcess = null;
        });

        // 将进程添加到扩展的订阅中，确保扩展关闭时清理进程
        context.subscriptions.push({
            dispose: () => {
                stopMastraServer();
            }
        });

    } catch (error) {
        console.error('Error starting Mastra server:', error);
        vscode.window.showErrorMessage(`启动Mastra服务时出错: ${error}`);
    }
}

function tryAlternativePort(context: vscode.ExtensionContext): void {
    // 尝试使用不同的端口
    const alternativePorts = [4112, 4113, 4114, 4115];
    
    for (const port of alternativePorts) {
        try {
            const extensionPath = context.extensionUri.fsPath;
            
            console.log(`Trying to start Mastra server on port ${port}...`);
            
            // 设置环境变量来指定端口
            const env = { ...process.env, PORT: port.toString() };
            
            mastraProcess = spawn('npx', ['mastra', 'dev'], {
                cwd: extensionPath,
                stdio: ['ignore', 'pipe', 'pipe'],
                shell: true,
                env: env
            });

            serverPort = port;
            
            if (mastraProcess.stdout) {
                mastraProcess.stdout.on('data', (data) => {
                    const output = data.toString();
                    console.log(`Mastra server (port ${port}) output:`, output);
                    
                    if (output.includes('Server running on') || output.includes(`localhost:${port}`)) {
                        vscode.window.showInformationMessage(`Mastra AI服务已启动在端口${port}`);
                    }
                });
            }

            break; // 如果成功启动，跳出循环
            
        } catch (error) {
            console.error(`Failed to start on port ${port}:`, error);
            continue;
        }
    }
}

export function stopMastraServer(): void {
    if (mastraProcess) {
        console.log('Stopping Mastra server...');
        
        // 在Windows上需要使用taskkill来终止进程树
        if (process.platform === 'win32') {
            spawn('taskkill', ['/pid', mastraProcess.pid!.toString(), '/t', '/f'], {
                stdio: 'ignore'
            });
        } else {
            mastraProcess.kill('SIGTERM');
        }
        
        mastraProcess = null;
        vscode.window.showInformationMessage('Mastra服务已停止');
    }
}

export function getMastraServerPort(): number {
    return serverPort;
}

export function isMastraServerRunning(): boolean {
    return mastraProcess !== null && !mastraProcess.killed;
}
