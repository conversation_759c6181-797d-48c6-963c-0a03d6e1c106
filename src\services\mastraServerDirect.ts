import * as vscode from 'vscode';
import * as http from 'http';
import { mastra } from '../mastra';

let server: http.Server | null = null;
let serverPort = 4111;

/**
 * 直接使用mastra实例启动HTTP服务器
 */
export function startMastraServerDirect(context: vscode.ExtensionContext): void {
    if (server) {
        console.log('Mastra server is already running');
        return;
    }

    try {
        // 从配置中读取端口
        const configPort = vscode.workspace.getConfiguration('aicode').get('mastraServerPort', 4111);
        serverPort = configPort;

        console.log('Starting Mastra server directly...');

        // 创建HTTP服务器
        server = http.createServer(async (req, res) => {
            try {
                // 设置CORS头
                res.setHeader('Access-Control-Allow-Origin', '*');
                res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
                res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

                // 处理OPTIONS预检请求
                if (req.method === 'OPTIONS') {
                    res.writeHead(200);
                    res.end();
                    return;
                }

                // 解析请求URL
                const url = new URL(req.url || '/', `http://${req.headers.host}`);
                const pathname = url.pathname;

                console.log(`${req.method} ${pathname}`);

                // 健康检查端点
                if (pathname === '/health') {
                    res.writeHead(200, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ status: 'ok', service: 'mastra' }));
                    return;
                }

                // Agent相关的API端点
                if (pathname.startsWith('/api/agents/')) {
                    await handleAgentRequest(req, res, pathname);
                    return;
                }

                // 默认响应
                res.writeHead(404, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: 'Not found' }));

            } catch (error) {
                console.error('Server request error:', error);
                res.writeHead(500, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: 'Internal server error' }));
            }
        });

        // 尝试启动服务器
        startServerOnPort(serverPort, context);

    } catch (error) {
        console.error('Error starting Mastra server:', error);
        vscode.window.showErrorMessage(`启动Mastra服务时出错: ${error}`);
    }
}

function startServerOnPort(port: number, context: vscode.ExtensionContext): void {
    if (!server) {
        return;
    }

    server.listen(port, 'localhost', () => {
        console.log(`Mastra server is running on http://localhost:${port}`);
        vscode.window.showInformationMessage(`Mastra AI服务已启动在端口${port}`);
        serverPort = port;
    });

    server.on('error', (error: any) => {
        if (error.code === 'EADDRINUSE') {
            console.log(`Port ${port} is busy, trying next port...`);
            const nextPort = port + 1;
            if (nextPort <= 4120) { // 限制尝试范围
                startServerOnPort(nextPort, context);
            } else {
                vscode.window.showErrorMessage('无法找到可用端口启动Mastra服务');
            }
        } else {
            console.error('Server error:', error);
            vscode.window.showErrorMessage(`Mastra服务启动失败: ${error.message}`);
        }
    });

    // 将服务器添加到扩展的订阅中
    context.subscriptions.push({
        dispose: () => {
            stopMastraServerDirect();
        }
    });
}

async function handleAgentRequest(req: http.IncomingMessage, res: http.ServerResponse, pathname: string): Promise<void> {
    try {
        // 解析路径，例如 /api/agents/weatherAgent/chat
        const pathParts = pathname.split('/').filter(Boolean);
        if (pathParts.length < 3) {
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Invalid agent path' }));
            return;
        }

        const agentName = pathParts[2]; // weatherAgent
        const action = pathParts[3] || 'info'; // chat, info, etc.

        // 获取agent实例
        const agent = mastra.getAgent(agentName as any); // 临时类型断言，因为mastra的类型定义比较严格
        if (!agent) {
            res.writeHead(404, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: `Agent '${agentName}' not found` }));
            return;
        }

        if (req.method === 'GET' && action === 'info') {
            // 返回agent信息
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                name: agent.name,
                instructions: agent.instructions,
                available: true
            }));
            return;
        }

        if (req.method === 'POST' && action === 'chat') {
            // 处理聊天请求
            let body = '';
            req.on('data', chunk => {
                body += chunk.toString();
            });

            req.on('end', async () => {
                try {
                    const { messages, message } = JSON.parse(body);
                    
                    // 准备消息格式
                    const chatMessages = messages || [{ role: 'user', content: message || '' }];
                    
                    // 调用agent
                    const result = await agent.stream(chatMessages);
                    
                    // 设置流式响应头
                    res.writeHead(200, {
                        'Content-Type': 'text/plain',
                        'Transfer-Encoding': 'chunked'
                    });

                    // 流式返回结果
                    for await (const chunk of result.textStream) {
                        res.write(chunk);
                    }
                    
                    res.end();
                    
                } catch (error) {
                    console.error('Agent chat error:', error);
                    res.writeHead(500, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ error: 'Agent processing failed' }));
                }
            });
            return;
        }

        // 不支持的方法或动作
        res.writeHead(405, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Method not allowed' }));

    } catch (error) {
        console.error('Handle agent request error:', error);
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Internal server error' }));
    }
}

export function stopMastraServerDirect(): void {
    if (server) {
        console.log('Stopping Mastra server...');
        server.close(() => {
            console.log('Mastra server stopped');
            vscode.window.showInformationMessage('Mastra服务已停止');
        });
        server = null;
    }
}

export function getMastraServerPortDirect(): number {
    return serverPort;
}

export function isMastraServerRunningDirect(): boolean {
    return server !== null && server.listening;
}
