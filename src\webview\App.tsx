import React, { useState, useEffect } from 'react';
import './App.css';
import ChatInterface from './components/ChatInterface';
import './components/ChatInterface.css';

// 声明vscode API类型
declare const acquireVsCodeApi: () => {
  postMessage(message: any): void;
  getState(): any;
  setState(state: any): void;
};

interface Message {
  command: string;
  data?: any;
}

const App: React.FC = () => {
  const [message, setMessage] = useState<string>('Hello from React!');
  const [count, setCount] = useState<number>(0);
  const [vscode] = useState(() => acquireVsCodeApi());

  useEffect(() => {
    // 监听来自VS Code扩展的消息
    const handleMessage = (event: MessageEvent) => {
      const message: Message = event.data;
      switch (message.command) {
        case 'updateMessage':
          setMessage(message.data);
          break;
        case 'updateCount':
          setCount(message.data);
          break;
      }
    };

    window.addEventListener('message', handleMessage);

    // 恢复状态
    const state = vscode.getState();
    if (state) {
      setMessage(state.message || message);
      setCount(state.count || count);
    }

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [vscode]);

  useEffect(() => {
    // 保存状态
    vscode.setState({ message, count });
  }, [message, count, vscode]);

  const sendMessageToExtension = () => {
    vscode.postMessage({
      command: 'alert',
      data: `Current count: ${count}`
    });
  };

  const incrementCount = () => {
    setCount(prev => prev + 1);
  };

  const resetCount = () => {
    setCount(0);
  };

  return (
    <div className="app">
      <header className="app-header">
        <h1>AI Code Extension</h1>
        <p>{message}</p>
      </header>
      <main className="app-main">
        <div className="chat-section">
          <ChatInterface vscode={vscode} />
        </div>

        <div className="counter-section">
          <h2>计数器: {count}</h2>
          <div className="button-group">
            <button onClick={incrementCount} className="btn btn-primary">
              增加
            </button>
            <button onClick={resetCount} className="btn btn-secondary">
              重置
            </button>
            <button onClick={sendMessageToExtension} className="btn btn-info">
              发送消息到扩展
            </button>
          </div>
        </div>

        <div className="info-section">
          <h3>功能说明</h3>
          <ul>
            <li>这是一个使用React开发的VS Code扩展页面</li>
            <li>可以与VS Code扩展进行双向通信</li>
            <li>状态会自动保存和恢复</li>
            <li>支持现代React特性（Hooks、TypeScript等）</li>
            <li>🆕 集成了 Mastra AI 聊天功能</li>
          </ul>
        </div>
      </main>
    </div>
  );
};

export default App;
