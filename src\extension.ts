import * as vscode from 'vscode';
import { ReactWebviewProvider } from './provider';

export function activate(context: vscode.ExtensionContext) {
    try {
        // 注册最简单的webview provider
        console.log('路由内容',context.extensionUri);
        // const provider = new MinimalWebviewProvider(context.extensionUri);
        const provider = new ReactWebviewProvider(context.extensionUri,'aicode.chatView');

        console.log('正在注册webview provider: aicode.chatView');
        const disposable = vscode.window.registerWebviewViewProvider(
            'aicode.chatView',
            provider,
            {
                webviewOptions: {
                    retainContextWhenHidden: true
                }
            }
        );
        console.log('Webview provider注册完成');

        // 注册一个简单命令来测试
        const commandDisposable = vscode.commands.registerCommand('aicode.test', () => {
            vscode.window.showInformationMessage('测试命令工作正常！');
        });

        // 注册一个命令来强制显示webview
        const showWebviewDisposable = vscode.commands.registerCommand('aicode.showWebview', () => {
            console.log('强制显示webview命令被调用');
            vscode.commands.executeCommand('workbench.view.extension.aicode');
        });

        // 注册配置 API Key 命令
        const configureApiKeyDisposable = vscode.commands.registerCommand('aicode.configureApiKey', async () => {
            const apiKey = await vscode.window.showInputBox({
                prompt: '请输入您的 OpenAI API Key',
                password: true,
                placeHolder: 'sk-...',
                ignoreFocusOut: true
            });

            if (apiKey) {
                await vscode.workspace.getConfiguration('aicode').update('openaiApiKey', apiKey, vscode.ConfigurationTarget.Global);
                vscode.window.showInformationMessage('OpenAI API Key 已保存！');
            }
        });

        // // 注册打开React页面命令
        const openReactPageDisposable = vscode.commands.registerCommand('aicode.openReactPage', () => {
            console.log('Opening React page...');
            // 创建一个新的webview面板
            const panel = vscode.window.createWebviewPanel(
                'aicode.reactPage',
                'React页面',
                vscode.ViewColumn.One,
                {
                    enableScripts: true,
                    localResourceRoots: [context.extensionUri]
                }
            );

            panel.webview.html = `
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>React页面</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            padding: 20px;
                            background: #1e1e1e;
                            color: #cccccc;
                        }
                        button {
                            background: #0e639c;
                            color: white;
                            border: none;
                            padding: 10px 20px;
                            cursor: pointer;
                            margin: 10px 0;
                            display: block;
                        }
                        button:hover { background: #1177bb; }
                    </style>
                </head>
                <body>
                    <h1>🚀 React页面</h1>
                    <p>这是通过命令面板打开的React页面！</p>
                    <button onclick="testMessage()">发送消息</button>
                    <p id="status">状态：页面已加载</p>

                    <script>
                        const vscode = acquireVsCodeApi();

                        function testMessage() {
                            vscode.postMessage({
                                command: 'showMessage',
                                text: '来自React页面的消息！'
                            });
                            document.getElementById('status').textContent = '状态：消息已发送';
                        }

                        console.log('React page loaded successfully');
                    </script>
                </body>
                </html>
            `;

            // 监听消息
            panel.webview.onDidReceiveMessage(message => {
                if (message.command === 'showMessage') {
                    vscode.window.showInformationMessage(message.text);
                }
            });
        });


        context.subscriptions.push(
            commandDisposable,
            disposable,
            openReactPageDisposable,
            showWebviewDisposable,
            configureApiKeyDisposable
        );

        console.log('所有组件注册完成，扩展激活成功');

    } catch (error) {
        console.error('Error during activation:', error);
        vscode.window.showErrorMessage(`扩展激活失败: ${error}`);
    }
}

export function deactivate() {
    console.log('=== AI code 插件已关闭 ===');
}