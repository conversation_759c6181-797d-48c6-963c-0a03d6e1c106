import { <PERSON><PERSON> } from "@mastra/core/mastra";
import { weatherAgent } from "./agents/weather-agent";
 import { registerCopilotKit } from "@ag-ui/mastra";


type WeatherRuntimeContext = {
  "user-id": string;
  "temperature-scale": "celsius" | "fahrenheit";
};

export const mastra = new Mastra({
  agents: { weatherAgent },
   server: {
    cors: {
      origin: "*",
      allowMethods: ["*"],
      allowHeaders: ["*"]
    },
    apiRoutes: [
      registerCopilotKit<WeatherRuntimeContext>({
        path: "/copilotkit",
        resourceId: "weatherAgent",
        setContext: (c, runtimeContext) => {
          runtimeContext.set("user-id", c.req.header("X-User-ID") || "anonymous");
          runtimeContext.set("temperature-scale", "celsius");
        }
      })
    ]
  }
});