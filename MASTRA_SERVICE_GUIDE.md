# Mastra 服务集成指南

## 概述

AI Code 扩展现在集成了 Mastra AI 框架，可以在插件运行时自动启动 Mastra 服务，无需手动运行 `npm run dev`。

## 功能特性

### 🚀 自动启动
- 插件激活时自动启动 Mastra 服务
- 默认端口：4111（可配置）
- 支持端口冲突自动切换

### 🔧 手动控制
- 通过命令面板启动/停止服务
- 实时查看服务状态
- 使用CLI模式启动

### ⚙️ 配置选项
- 自动启动开关
- 自定义服务端口

## 使用方法

### 1. 自动启动（推荐）

插件默认会在激活时自动启动 Mastra 服务：

1. 安装并启用 AI Code 扩展
2. 服务会自动在端口 4111 启动
3. 如果端口被占用，会自动尝试其他端口（4112, 4113, 等）

### 2. 手动控制

通过命令面板控制服务：

1. 按 `Ctrl+Shift+P`（Windows/Linux）或 `Cmd+Shift+P`（Mac）
2. 输入以下命令：
   - `启动/停止 Mastra 服务 (CLI)` - 切换服务状态
   - `查看 Mastra 服务状态` - 检查当前状态

### 3. 配置设置

在 VS Code 设置中配置：

```json
{
  "aicode.autoStartMastraServer": true,    // 自动启动（默认：true）
  "aicode.mastraServerPort": 4111          // 服务端口（默认：4111）
}
```

## 启动方式

### CLI 启动
- 使用 `mastra dev` 命令
- 功能完整，包含所有Mastra特性
- 包含完整的开发工具和调试功能
- 提供完整的API端点和管理界面

## API 端点

服务启动后，可以通过以下端点访问：

### 健康检查
```
GET http://localhost:4111/health
```

### Agent 信息
```
GET http://localhost:4111/api/agents/{agentName}/info
```

### Agent 聊天
```
POST http://localhost:4111/api/agents/{agentName}/chat
Content-Type: application/json

{
  "message": "你好",
  "messages": [
    {"role": "user", "content": "你好"}
  ]
}
```

## 故障排除

### 服务无法启动
1. 检查端口是否被占用
2. 确保 Node.js 和 npm 已正确安装
3. 查看 VS Code 开发者控制台的错误信息

### 端口冲突
- 服务会自动尝试其他端口
- 可以在设置中修改默认端口
- 检查防火墙设置

### 性能问题
- 如果不需要自动启动，可以在设置中关闭
- 使用直接启动模式而不是 CLI 模式
- 确保系统有足够的内存

## 开发说明

### 添加新的 Agent
1. 在 `src/mastra/agents/` 目录下创建新的 agent 文件
2. 在 `src/mastra/index.ts` 中注册新的 agent
3. 重新编译和打包插件

### 自定义 API 端点
1. 修改 `src/services/mastraServerDirect.ts`
2. 添加新的路由处理逻辑
3. 更新 API 文档

### 测试
运行测试：
```bash
npm run test
```

## 版本历史

- v0.0.2: 添加 Mastra 服务集成
- v0.0.1: 基础 AI Code 功能

## 支持

如果遇到问题，请：
1. 查看 VS Code 开发者控制台
2. 检查插件输出面板
3. 提交 Issue 到项目仓库
