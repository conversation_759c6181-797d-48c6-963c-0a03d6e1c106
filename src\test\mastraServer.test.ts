import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import * as vscode from 'vscode';
import { startMastraServerDirect, stopMastraServerDirect, isMastraServerRunningDirect } from '../services/mastraServerDirect';

// Mock vscode module
const mockContext = {
    extensionUri: { fsPath: process.cwd() },
    subscriptions: []
} as any;

describe('Mastra Server', () => {
    beforeAll(async () => {
        // 模拟vscode环境
        (global as any).vscode = {
            workspace: {
                getConfiguration: () => ({
                    get: (key: string, defaultValue: any) => {
                        if (key === 'mastraServerPort') {
                            return 4112; // 使用测试端口
                        }
                        return defaultValue;
                    }
                })
            },
            window: {
                showInformationMessage: (msg: string) => console.log('Info:', msg),
                showErrorMessage: (msg: string) => console.log('Error:', msg)
            }
        };
    });

    afterAll(async () => {
        // 清理
        stopMastraServerDirect();
    });

    it('should start and stop server correctly', async () => {
        // 启动服务器
        startMastraServerDirect(mockContext);
        
        // 等待服务器启动
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 检查服务器是否运行
        expect(isMastraServerRunningDirect()).toBe(true);
        
        // 停止服务器
        stopMastraServerDirect();
        
        // 等待服务器停止
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 检查服务器是否停止
        expect(isMastraServerRunningDirect()).toBe(false);
    });

    it('should respond to health check', async () => {
        // 启动服务器
        startMastraServerDirect(mockContext);
        
        // 等待服务器启动
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        try {
            // 测试健康检查端点
            const response = await fetch('http://localhost:4112/health');
            const data = await response.json();
            
            expect(response.status).toBe(200);
            expect(data.status).toBe('ok');
            expect(data.service).toBe('mastra');
        } catch (error) {
            console.error('Health check failed:', error);
            throw error;
        } finally {
            // 清理
            stopMastraServerDirect();
        }
    });

    it('should handle agent info request', async () => {
        // 启动服务器
        startMastraServerDirect(mockContext);
        
        // 等待服务器启动
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        try {
            // 测试agent信息端点
            const response = await fetch('http://localhost:4112/api/agents/weatherAgent/info');
            
            if (response.status === 200) {
                const data = await response.json();
                expect(data.name).toBeDefined();
                expect(data.available).toBe(true);
            } else {
                // 如果agent不存在，应该返回404
                expect(response.status).toBe(404);
            }
        } catch (error) {
            console.error('Agent info request failed:', error);
            throw error;
        } finally {
            // 清理
            stopMastraServerDirect();
        }
    });
});
