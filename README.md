# AI Code Extension

一个集成了 Mastra AI 框架的 VS Code 扩展，提供智能代码助手功能。

## 功能特性

- 🤖 **AI 聊天助手**: 基于 OpenAI GPT 模型的智能对话
- 🔧 **代码辅助**: 帮助解决编程问题和调试
- 📝 **React 界面**: 现代化的用户界面
- ⚡ **流式响应**: 实时的 AI 回复体验
- 🔐 **安全配置**: 本地存储 API Key
- ✅ **React支持**: 使用React开发插件页面，支持现代React特性（Hooks、TypeScript等）
- ✅ **双向通信**: 扩展与React页面之间的消息通信
- ✅ **状态管理**: 自动保存和恢复页面状态
- ✅ **VS Code主题**: 自动适配VS Code的主题样式
- 🚀 **Mastra服务**: 内置Mastra AI框架服务，支持智能代理
- 🔄 **自动启动**: 插件激活时自动启动Mastra CLI服务
- ⚙️ **服务控制**: 手动启动/停止Mastra CLI服务

## 安装和配置

### 1. 配置 OpenAI API Key

在使用 AI 功能之前，需要配置您的 OpenAI API Key：

1. 打开 VS Code 命令面板 (`Ctrl+Shift+P` 或 `Cmd+Shift+P`)
2. 搜索并执行 "配置 OpenAI API Key" 命令
3. 输入您的 OpenAI API Key (格式: sk-...)

或者在 VS Code 设置中手动配置：
- 打开设置 (`Ctrl+,` 或 `Cmd+,`)
- 搜索 "aicode.openaiApiKey"
- 输入您的 API Key

### 2. Mastra 服务配置

插件会在激活时自动启动 Mastra AI 服务。您可以通过以下方式控制服务：

#### 自动启动配置
- 打开 VS Code 设置
- 搜索 "aicode.autoStartMastraServer"
- 设置为 `true`（默认）自动启动，或 `false` 手动启动

#### 端口配置
- 搜索 "aicode.mastraServerPort"
- 设置服务端口（默认：4111）

#### 手动控制服务
1. 打开命令面板 (`Ctrl+Shift+P` 或 `Cmd+Shift+P`)
2. 执行以下命令：
   - **"启动/停止 Mastra 服务 (CLI)"**: 切换服务状态
   - **"查看 Mastra 服务状态"**: 检查服务运行状态

#### 启动方式
插件使用CLI方式启动Mastra服务：
- 使用 `mastra dev` 命令启动完整的开发服务器
- 包含所有Mastra功能和开发工具

### 3. 使用 AI 聊天功能

1. 打开命令面板 (`Ctrl+Shift+P` 或 `Cmd+Shift+P`)
2. 搜索并执行 "显示AI Code面板" 命令
3. 在侧边栏中找到 AI Code 面板
4. 开始与 AI 助手对话

## Requirements

- Node.js 18+
- VS Code 1.74+
- OpenAI API Key (用于 AI 功能)

## Extension Settings

本扩展提供以下配置选项：

* `aicode.openaiApiKey`: OpenAI API Key，用于 AI 聊天功能

## 命令列表

| 命令 | 描述 |
|------|------|
| `aicode.showWebview` | 显示 AI Code 面板 |
| `aicode.configureApiKey` | 配置 OpenAI API Key |
| `aicode.openReactPage` | 打开 React 页面 |

## Known Issues

Calling out known issues can help limit users opening duplicate issues against your extension.

## Release Notes

Users appreciate release notes as you update your extension.

### 1.0.0

Initial release of ...

### 1.0.1

Fixed issue #.

### 1.1.0

Added features X, Y, and Z.

---

## Following extension guidelines

Ensure that you've read through the extensions guidelines and follow the best practices for creating your extension.

* [Extension Guidelines](https://code.visualstudio.com/api/references/extension-guidelines)

## Working with Markdown

You can author your README using Visual Studio Code. Here are some useful editor keyboard shortcuts:

* Split the editor (`Cmd+\` on macOS or `Ctrl+\` on Windows and Linux).
* Toggle preview (`Shift+Cmd+V` on macOS or `Shift+Ctrl+V` on Windows and Linux).
* Press `Ctrl+Space` (Windows, Linux, macOS) to see a list of Markdown snippets.

## For more information

* [Visual Studio Code's Markdown Support](http://code.visualstudio.com/docs/languages/markdown)
* [Markdown Syntax Reference](https://help.github.com/articles/markdown-basics/)

**Enjoy!**
