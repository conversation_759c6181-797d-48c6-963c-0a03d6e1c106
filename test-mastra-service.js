#!/usr/bin/env node

/**
 * 测试 Mastra 服务的简单脚本
 * 使用方法：node test-mastra-service.js [port]
 */

const http = require('http');

const port = process.argv[2] || 4111;
const baseUrl = `http://localhost:${port}`;

console.log(`🧪 测试 Mastra 服务 (${baseUrl})`);
console.log('=' .repeat(50));

// 测试健康检查
async function testHealth() {
    console.log('\n📋 测试健康检查...');
    try {
        const response = await fetch(`${baseUrl}/health`);
        const data = await response.json();
        
        if (response.status === 200 && data.status === 'ok') {
            console.log('✅ 健康检查通过');
            console.log(`   状态: ${data.status}`);
            console.log(`   服务: ${data.service}`);
            return true;
        } else {
            console.log('❌ 健康检查失败');
            console.log(`   状态码: ${response.status}`);
            return false;
        }
    } catch (error) {
        console.log('❌ 健康检查失败');
        console.log(`   错误: ${error.message}`);
        return false;
    }
}

// 测试 Agent 信息
async function testAgentInfo() {
    console.log('\n🤖 测试 Agent 信息...');
    try {
        const response = await fetch(`${baseUrl}/api/agents/weatherAgent/info`);
        
        if (response.status === 200) {
            const data = await response.json();
            console.log('✅ Agent 信息获取成功');
            console.log(`   名称: ${data.name}`);
            console.log(`   可用: ${data.available}`);
            return true;
        } else if (response.status === 404) {
            console.log('⚠️  Agent 不存在');
            return false;
        } else {
            console.log('❌ Agent 信息获取失败');
            console.log(`   状态码: ${response.status}`);
            return false;
        }
    } catch (error) {
        console.log('❌ Agent 信息获取失败');
        console.log(`   错误: ${error.message}`);
        return false;
    }
}

// 测试 Agent 聊天
async function testAgentChat() {
    console.log('\n💬 测试 Agent 聊天...');
    try {
        const response = await fetch(`${baseUrl}/api/agents/weatherAgent/chat`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                message: '你好，请介绍一下自己'
            })
        });
        
        if (response.status === 200) {
            console.log('✅ Agent 聊天测试开始');
            console.log('📝 响应内容:');
            
            // 读取流式响应
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let content = '';
            
            while (true) {
                const { done, value } = await reader.read();
                if (done) break;
                
                const chunk = decoder.decode(value);
                content += chunk;
                process.stdout.write(chunk);
            }
            
            console.log('\n✅ Agent 聊天测试完成');
            return true;
        } else {
            console.log('❌ Agent 聊天测试失败');
            console.log(`   状态码: ${response.status}`);
            return false;
        }
    } catch (error) {
        console.log('❌ Agent 聊天测试失败');
        console.log(`   错误: ${error.message}`);
        return false;
    }
}

// 主测试函数
async function runTests() {
    console.log('开始测试...\n');
    
    const results = {
        health: false,
        agentInfo: false,
        agentChat: false
    };
    
    // 测试健康检查
    results.health = await testHealth();
    
    if (!results.health) {
        console.log('\n❌ 服务未运行或无法访问');
        console.log('请确保：');
        console.log('1. AI Code 扩展已启用');
        console.log('2. Mastra 服务已启动');
        console.log('3. 端口号正确');
        process.exit(1);
    }
    
    // 测试 Agent 信息
    results.agentInfo = await testAgentInfo();
    
    // 测试 Agent 聊天（仅在 Agent 存在时）
    if (results.agentInfo) {
        results.agentChat = await testAgentChat();
    }
    
    // 输出测试结果
    console.log('\n' + '='.repeat(50));
    console.log('📊 测试结果汇总:');
    console.log(`   健康检查: ${results.health ? '✅' : '❌'}`);
    console.log(`   Agent信息: ${results.agentInfo ? '✅' : '❌'}`);
    console.log(`   Agent聊天: ${results.agentChat ? '✅' : '❌'}`);
    
    const passedTests = Object.values(results).filter(Boolean).length;
    const totalTests = Object.keys(results).length;
    
    console.log(`\n🎯 通过率: ${passedTests}/${totalTests} (${Math.round(passedTests/totalTests*100)}%)`);
    
    if (passedTests === totalTests) {
        console.log('🎉 所有测试通过！Mastra 服务运行正常。');
        process.exit(0);
    } else {
        console.log('⚠️  部分测试失败，请检查服务配置。');
        process.exit(1);
    }
}

// 检查是否支持 fetch API
if (typeof fetch === 'undefined') {
    console.log('❌ 此脚本需要 Node.js 18+ 或安装 node-fetch');
    console.log('请升级 Node.js 或运行: npm install node-fetch');
    process.exit(1);
}

// 运行测试
runTests().catch(error => {
    console.error('❌ 测试运行失败:', error);
    process.exit(1);
});
