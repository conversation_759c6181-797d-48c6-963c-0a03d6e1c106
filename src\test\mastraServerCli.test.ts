import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import * as vscode from 'vscode';
import { startMastraServer, stopMastraServer, isMastraServerRunning } from '../services/mastraServer';

// Mock vscode module
const mockContext = {
    extensionUri: { fsPath: process.cwd() },
    subscriptions: []
} as any;

describe('Mastra CLI Server', () => {
    beforeAll(async () => {
        // 模拟vscode环境
        (global as any).vscode = {
            workspace: {
                getConfiguration: () => ({
                    get: (key: string, defaultValue: any) => {
                        if (key === 'mastraServerPort') {
                            return 4113; // 使用测试端口
                        }
                        return defaultValue;
                    }
                })
            },
            window: {
                showInformationMessage: (msg: string) => console.log('Info:', msg),
                showErrorMessage: (msg: string) => console.log('Error:', msg)
            }
        };
    });

    afterAll(async () => {
        // 清理
        stopMastraServer();
        // 等待进程完全停止
        await new Promise(resolve => setTimeout(resolve, 2000));
    });

    it('should start and stop CLI server correctly', async () => {
        // 启动服务器
        startMastraServer(mockContext);
        
        // 等待服务器启动
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // 检查服务器是否运行
        expect(isMastraServerRunning()).toBe(true);
        
        // 停止服务器
        stopMastraServer();
        
        // 等待服务器停止
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 检查服务器是否停止
        expect(isMastraServerRunning()).toBe(false);
    });

    it('should respond to health check after CLI startup', async () => {
        // 启动服务器
        startMastraServer(mockContext);
        
        // 等待服务器启动（CLI启动需要更长时间）
        await new Promise(resolve => setTimeout(resolve, 8000));
        
        try {
            // 测试健康检查端点（使用默认端口4111，因为CLI会使用默认配置）
            const response = await fetch('http://localhost:4111/health');
            
            if (response.ok) {
                const data = await response.json();
                expect(response.status).toBe(200);
                // CLI服务器可能有不同的响应格式
                console.log('Health check response:', data);
            } else {
                console.log('Health check failed with status:', response.status);
                // CLI服务器可能没有/health端点，这是正常的
            }
        } catch (error) {
            console.log('Health check request failed (expected for CLI):', (error as Error).message);
            // CLI服务器可能使用不同的端点结构，这是正常的
        } finally {
            // 清理
            stopMastraServer();
        }
    });

    it('should handle multiple start/stop cycles', async () => {
        // 第一次启动
        startMastraServer(mockContext);
        await new Promise(resolve => setTimeout(resolve, 3000));
        expect(isMastraServerRunning()).toBe(true);
        
        // 停止
        stopMastraServer();
        await new Promise(resolve => setTimeout(resolve, 2000));
        expect(isMastraServerRunning()).toBe(false);
        
        // 第二次启动
        startMastraServer(mockContext);
        await new Promise(resolve => setTimeout(resolve, 3000));
        expect(isMastraServerRunning()).toBe(true);
        
        // 最终清理
        stopMastraServer();
        await new Promise(resolve => setTimeout(resolve, 2000));
    });
});
