import React, { useState, useRef, useEffect } from 'react';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

interface ChatInterfaceProps {
  vscode: any;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({ vscode }) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const currentStreamingMessage = useRef<string>('');

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    // 监听来自扩展的消息
    const handleMessage = (event: MessageEvent) => {
      const message = event.data;
      
      switch (message.command) {
        case 'aiResponse':
          setIsLoading(false);
          setIsStreaming(false);
          const assistantMessage: Message = {
            id: Date.now().toString(),
            role: 'assistant',
            content: message.content,
            timestamp: new Date()
          };
          setMessages(prev => [...prev, assistantMessage]);
          break;
          
        case 'aiStreamChunk':
          setIsStreaming(true);
          currentStreamingMessage.current += message.content;
          // 更新最后一条消息或创建新的流式消息
          setMessages(prev => {
            const lastMessage = prev[prev.length - 1];
            if (lastMessage && lastMessage.role === 'assistant' && lastMessage.id === 'streaming') {
              return [
                ...prev.slice(0, -1),
                { ...lastMessage, content: currentStreamingMessage.current }
              ];
            } else {
              return [
                ...prev,
                {
                  id: 'streaming',
                  role: 'assistant',
                  content: currentStreamingMessage.current,
                  timestamp: new Date()
                }
              ];
            }
          });
          break;
          
        case 'aiStreamEnd':
          setIsLoading(false);
          setIsStreaming(false);
          // 将流式消息转换为正式消息
          setMessages(prev => {
            const lastMessage = prev[prev.length - 1];
            if (lastMessage && lastMessage.id === 'streaming') {
              return [
                ...prev.slice(0, -1),
                { ...lastMessage, id: Date.now().toString() }
              ];
            }
            return prev;
          });
          currentStreamingMessage.current = '';
          break;
          
        case 'aiError':
          setIsLoading(false);
          setIsStreaming(false);
          const errorMessage: Message = {
            id: Date.now().toString(),
            role: 'assistant',
            content: `❌ 错误: ${message.error}`,
            timestamp: new Date()
          };
          setMessages(prev => [...prev, errorMessage]);
          break;
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  const sendMessage = async () => {
    if (!input.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: input.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);
    currentStreamingMessage.current = '';

    // 发送消息到扩展
    vscode.postMessage({
      command: 'sendChatMessage',
      message: input.trim(),
      messages: messages.map(msg => ({
        role: msg.role,
        content: msg.content
      }))
    });
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const clearChat = () => {
    setMessages([]);
    currentStreamingMessage.current = '';
  };

  return (
    <div className="chat-interface">
      <div className="chat-header">
        <h3>🤖 AI 代码助手</h3>
        <button onClick={clearChat} className="clear-btn" disabled={isLoading}>
          清空对话
        </button>
      </div>
      
      <div className="chat-messages">
        {messages.map((message) => (
          <div key={message.id} className={`message ${message.role}`}>
            <div className="message-header">
              <span className="role">
                {message.role === 'user' ? '👤 你' : '🤖 AI'}
              </span>
              <span className="timestamp">
                {message.timestamp.toLocaleTimeString()}
              </span>
            </div>
            <div className="message-content">
              <div style={{ whiteSpace: 'pre-wrap' }}>
                {message.content}
              </div>
            </div>
          </div>
        ))}
        
        {isLoading && !isStreaming && (
          <div className="message assistant">
            <div className="message-header">
              <span className="role">🤖 AI</span>
            </div>
            <div className="message-content">
              <div className="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>
      
      <div className="chat-input">
        <textarea
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="输入你的问题... (Enter 发送, Shift+Enter 换行)"
          disabled={isLoading}
          rows={3}
        />
        <button 
          onClick={sendMessage} 
          disabled={!input.trim() || isLoading}
          className="send-btn"
        >
          {isLoading ? '发送中...' : '发送'}
        </button>
      </div>
    </div>
  );
};

export default ChatInterface;
