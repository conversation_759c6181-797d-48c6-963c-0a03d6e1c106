/* VS Code主题变量 */
:root {
  --vscode-foreground: var(--vscode-editor-foreground);
  --vscode-background: var(--vscode-editor-background);
  --vscode-button-background: var(--vscode-button-background);
  --vscode-button-foreground: var(--vscode-button-foreground);
  --vscode-button-hoverBackground: var(--vscode-button-hoverBackground);
  --vscode-input-background: var(--vscode-input-background);
  --vscode-input-border: var(--vscode-input-border);
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--vscode-font-family);
  font-size: var(--vscode-font-size);
  color: var(--vscode-foreground);
  background-color: var(--vscode-background);
  line-height: 1.6;
}

.app {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.app-header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--vscode-input-border);
}

.app-header h1 {
  font-size: 2rem;
  margin-bottom: 10px;
  color: var(--vscode-foreground);
}

.app-header p {
  font-size: 1.1rem;
  opacity: 0.8;
}

.app-main {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.chat-section {
  margin-bottom: 20px;
}

.counter-section {
  text-align: center;
  padding: 20px;
  background-color: var(--vscode-input-background);
  border-radius: 8px;
  border: 1px solid var(--vscode-input-border);
}

.counter-section h2 {
  font-size: 1.5rem;
  margin-bottom: 20px;
  color: var(--vscode-foreground);
}

.button-group {
  display: flex;
  gap: 10px;
  justify-content: center;
  flex-wrap: wrap;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s ease;
  min-width: 80px;
}

.btn-primary {
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
}

.btn-primary:hover {
  background-color: var(--vscode-button-hoverBackground);
}

.btn-secondary {
  background-color: transparent;
  color: var(--vscode-foreground);
  border: 1px solid var(--vscode-input-border);
}

.btn-secondary:hover {
  background-color: var(--vscode-input-background);
}

.btn-info {
  background-color: #007acc;
  color: white;
}

.btn-info:hover {
  background-color: #005a9e;
}

.info-section {
  padding: 20px;
  background-color: var(--vscode-input-background);
  border-radius: 8px;
  border: 1px solid var(--vscode-input-border);
}

.info-section h3 {
  margin-bottom: 15px;
  color: var(--vscode-foreground);
}

.info-section ul {
  list-style-type: none;
  padding-left: 0;
}

.info-section li {
  padding: 5px 0;
  position: relative;
  padding-left: 20px;
}

.info-section li::before {
  content: "•";
  color: var(--vscode-button-background);
  font-weight: bold;
  position: absolute;
  left: 0;
}

/* 响应式设计 */
@media (max-width: 600px) {
  .app {
    padding: 15px;
  }
  
  .button-group {
    flex-direction: column;
    align-items: center;
  }
  
  .btn {
    width: 100%;
    max-width: 200px;
  }
}
