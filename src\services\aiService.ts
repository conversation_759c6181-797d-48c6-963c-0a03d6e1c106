
import { generateText, streamText, CoreMessage } from 'ai';
import * as vscode from 'vscode';
import { AgentService } from '../types/agent';
import { openai } from '../llm/open';
import { mastra } from '../mastra';





export class MastraAIService implements AgentService {
    private apiKey: string | undefined;

    constructor() {
        this.apiKey = vscode.workspace.getConfiguration('aicode').get('openaiApiKey');
    }

    private getOpenAIModel() {
        if (!this.apiKey) {
            throw new Error('OpenAI API key not configured');
        }
        return openai('deepseek-ai/DeepSeek-V3');
    }

    async query(
        prompt?: string,
        messages?: CoreMessage[],
        options?: any,
        abortController?: AbortController,
        onMessage?: (message: any) => void
    ): Promise<any[]> {
        if (!this.hasApiKey()) {
            throw new Error('OpenAI API key not configured. Please set it in VS Code settings.');
        }
        console.log('prompt', prompt);

        try {
            const model = this.getOpenAIModel();
            console.log('model', model);
         
            // 如果有流式回调，使用 streamText
            if (onMessage) {

              const weatherAgent =  mastra.getAgent('weatherAgent');
              const result = await  weatherAgent.stream(messages || [{ role: 'user', content: prompt || '' }]);
              // const result =  streamText({
                //     model: model,
                //     messages: messages || [{ role: 'user', content: prompt || '' }],
                //     abortSignal: abortController?.signal,
                // });
                //    console.log('result', result.textStream);

                const chunks: any[] = [];
                for await (const chunk of result.textStream) {
                    console.log('返回的内容',chunk);
                    chunks.push({ type: 'text', content: chunk });
                    onMessage({ type: 'text', content: chunk });
                }
                return chunks;
            } else {
                   console.log('messages2', messages);
                // 否则使用 generateText
                const result = await generateText({
                    model: model,
                    messages: messages || [{ role: 'user', content: prompt || '' }],
                    abortSignal: abortController?.signal,
                });
                console.log('result', result);
                return [{ type: 'text', content: result.text }];
            }
        } catch (error: any) {
            console.error('Error querying AI:', error);
            if (this.isApiKeyAuthError(error.message)) {
                throw new Error('Invalid OpenAI API key. Please check your configuration.');
            }
            throw error;
        }
    }

    hasApiKey(): boolean {
        return !!this.apiKey && this.apiKey.trim().length > 0;
    }

    isApiKeyAuthError(errorMessage: string): boolean {
        const message = errorMessage.toLowerCase();
        return message.includes('401') ||
            message.includes('unauthorized') ||
            message.includes('invalid api key') ||
            message.includes('authentication');
    }

    // 更新 API Key
    updateApiKey(apiKey: string) {
        this.apiKey = apiKey;
    }

    // 获取当前配置的模型信息
    getModelInfo() {
        return {
            name: 'DeepSeek-V3',
            provider: 'DeepSeek AI',
            description: 'Advanced language model for coding assistance'
        };
    }
}
