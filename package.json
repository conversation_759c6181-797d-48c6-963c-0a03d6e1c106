{"name": "aicode", "displayName": "aicode", "description": "", "version": "0.0.4", "publisher": "aicode-dev", "icon": "icon.png", "engines": {"vscode": "^1.102.0"}, "categories": ["Other"], "activationEvents": ["onStartupFinished"], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "aicode.test", "title": "测试命令"}, {"command": "aicode.openReactPage", "title": "打开React页面"}, {"command": "aicode.showWebview", "title": "显示AI Code面板"}, {"command": "aicode.configureApiKey", "title": "配置 OpenAI API Key"}, {"command": "aicode.toggleMastraServer", "title": "启动/停止 Mastra 服务 (CLI)"}, {"command": "aicode.mastraServerStatus", "title": "查看 Mastra 服务状态"}], "viewsContainers": {"activitybar": [{"id": "aicode", "title": "AI Code", "icon": "icon.png"}]}, "views": {"aicode": [{"id": "aicode.chatView", "name": "React Panel", "when": "true", "type": "webview"}], "explorer": [{"id": "aicode.explorer<PERSON>iew", "name": "AI Code React", "when": "true"}]}, "menus": {"commandPalette": [{"command": "aicode.openReactPage", "when": "true"}, {"command": "aicode.toggleMastraServer", "when": "true"}, {"command": "aicode.mastraServerStatus", "when": "true"}]}, "configuration": {"title": "AI Code", "properties": {"aicode.openaiApiKey": {"type": "string", "default": "", "description": "OpenAI API Key for AI chat functionality", "scope": "application"}, "aicode.autoStartMastraServer": {"type": "boolean", "default": true, "description": "Automatically start Mastra server when extension activates", "scope": "application"}, "aicode.mastraServerPort": {"type": "number", "default": 4111, "description": "Port for Mastra server (default: 4111)", "scope": "application"}}}}, "scripts": {"dev": "<PERSON>ra dev", "build": "mastra build", "packagex": "pnpm vsce package --no-dependencies", "vscode:prepublish": "pnpm run package", "compile": "pnpm run check-types && pnpm run lint && node esbuild.js", "test": "vitest", "test:run": "vitest run", "test:watch": "vitest --watch", "watch": "npm-run-all -p watch:*", "watch:esbuild": "node esbuild.js --watch", "watch:tsc": "tsc --noEmit --watch --project tsconfig.json", "package": "pnpm run check-types && pnpm run lint && node esbuild.js --production", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "pnpm run compile-tests && pnpm run compile && pnpm run lint", "check-types": "tsc --noEmit", "lint": "eslint src"}, "devDependencies": {"@types/mocha": "^10.0.10", "@types/node": "20.x", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@types/vscode": "^1.102.0", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "@vscode/test-cli": "^0.0.11", "@vscode/test-electron": "^2.5.2", "esbuild": "^0.25.3", "eslint": "^9.25.1", "npm-run-all": "^4.1.5", "typescript": "^5.8.3"}, "dependencies": {"@ag-ui/mastra": "^0.0.5", "@ai-sdk/openai": "^1.3.23", "@copilotkit/react-core": "^1.9.3", "@copilotkit/react-ui": "^1.9.3", "@copilotkit/runtime": "^1.9.3", "@mastra/core": "^0.12.0", "@mastra/libsql": "^0.12.0", "ai": "^4.3.19", "mastra": "^0.10.16", "react": "^19.1.1", "react-dom": "^19.1.1", "react-markdown": "^10.1.0", "vitest": "^3.2.4", "zod": "3.23.8"}}